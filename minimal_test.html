<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.css" />
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 400px; width: 100%; margin: 20px 0; }
        .status { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 20px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>Pac-Map 最小測試</h1>
    <div id="status"></div>
    <button onclick="testBasics()">測試基本功能</button>
    <button onclick="testBackend()">測試後端連接</button>
    <button onclick="testMapLoad()">測試地圖載入</button>
    <div id="map"></div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.js"></script>
    <script>
        const status = document.getElementById('status');
        let map = null;
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            status.appendChild(div);
            console.log(message);
        }
        
        function testBasics() {
            addStatus('開始基本功能測試...', 'info');
            
            try {
                // 測試 JavaScript 基本功能
                const test = { a: 1, b: 2 };
                addStatus('✅ JavaScript 基本功能正常', 'success');
                
                // 測試 Leaflet
                if (typeof L !== 'undefined') {
                    addStatus('✅ Leaflet 庫載入成功', 'success');
                } else {
                    addStatus('❌ Leaflet 庫未載入', 'error');
                }
                
                // 測試 fetch API
                if (typeof fetch !== 'undefined') {
                    addStatus('✅ Fetch API 可用', 'success');
                } else {
                    addStatus('❌ Fetch API 不可用', 'error');
                }
                
                addStatus('基本功能測試完成', 'info');
                
            } catch (error) {
                addStatus('❌ 基本功能測試失敗: ' + error.message, 'error');
            }
        }
        
        async function testBackend() {
            addStatus('開始後端連接測試...', 'info');
            
            try {
                const response = await fetch('http://localhost:8000/maps/configs');
                if (response.ok) {
                    const data = await response.json();
                    addStatus('✅ 後端連接成功，地圖配置數量: ' + data.data.length, 'success');
                    return data;
                } else {
                    addStatus('❌ 後端響應錯誤: ' + response.status, 'error');
                    return null;
                }
            } catch (error) {
                addStatus('❌ 後端連接失敗: ' + error.message, 'error');
                return null;
            }
        }
        
        async function testMapLoad() {
            addStatus('開始地圖載入測試...', 'info');
            
            try {
                // 初始化地圖
                if (map) {
                    map.remove();
                }
                
                map = L.map('map').setView([25.0330, 121.5654], 13);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
                addStatus('✅ 基本地圖載入成功', 'success');
                
                // 測試後端地圖數據
                const response = await fetch('http://localhost:8000/maps/0/data');
                if (response.ok) {
                    const mapData = await response.json();
                    addStatus('✅ 後端地圖數據載入成功', 'success');
                    addStatus('地圖名稱: ' + mapData.map_name, 'info');
                    addStatus('路網段數: ' + mapData.road_network.length, 'info');
                    addStatus('有效位置數: ' + mapData.valid_positions.length, 'info');
                    addStatus('POI 數量: ' + mapData.pois.length, 'info');
                    
                    // 在地圖上顯示一些數據點
                    if (mapData.valid_positions.length > 0) {
                        const samplePositions = mapData.valid_positions.slice(0, 10);
                        samplePositions.forEach((pos, index) => {
                            L.circleMarker([pos[0], pos[1]], {
                                radius: 3,
                                color: 'blue',
                                fillOpacity: 0.7
                            }).addTo(map).bindPopup(`位置 ${index + 1}`);
                        });
                        addStatus('✅ 在地圖上顯示了 ' + samplePositions.length + ' 個樣本位置', 'success');
                    }
                    
                } else {
                    addStatus('❌ 後端地圖數據載入失敗: ' + response.status, 'error');
                }
                
            } catch (error) {
                addStatus('❌ 地圖載入測試失敗: ' + error.message, 'error');
            }
        }
        
        // 頁面載入完成後自動執行基本測試
        window.addEventListener('load', () => {
            addStatus('頁面載入完成，開始自動測試...', 'info');
            testBasics();
        });
    </script>
</body>
</html>

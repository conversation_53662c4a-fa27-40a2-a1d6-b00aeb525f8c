<!DOCTYPE html>
<html>
<head>
    <title>Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { margin: 5px 0; padding: 5px; background: #f0f0f0; }
        .error { background: #ffcccc; }
        .success { background: #ccffcc; }
    </style>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="logs"></div>
    
    <script>
        const logs = document.getElementById('logs');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logs.appendChild(div);
            console.log(message);
        }
        
        log('開始測試...');
        
        // 測試基本 JavaScript
        try {
            log('測試基本 JavaScript 功能...');
            const test = { a: 1, b: 2 };
            log('✅ 基本 JavaScript 正常');
        } catch (error) {
            log('❌ 基本 JavaScript 錯誤: ' + error.message, 'error');
        }
        
        // 測試 fetch
        try {
            log('測試 fetch API...');
            fetch('http://localhost:8000/maps/configs')
                .then(response => {
                    if (response.ok) {
                        log('✅ 後端連接正常');
                        return response.json();
                    } else {
                        log('❌ 後端響應錯誤: ' + response.status, 'error');
                    }
                })
                .then(data => {
                    if (data) {
                        log('✅ 後端數據正常: ' + data.data.length + ' 個地圖配置');
                    }
                })
                .catch(error => {
                    log('❌ 後端連接失敗: ' + error.message, 'error');
                });
        } catch (error) {
            log('❌ fetch API 錯誤: ' + error.message, 'error');
        }
        
        // 測試模組載入
        log('測試模組載入...');
        
        // 測試 gameState
        import('./js/gameState.js')
            .then(module => {
                log('✅ gameState.js 載入成功');
                if (module.gameState) {
                    log('✅ gameState 對象存在');
                } else {
                    log('❌ gameState 對象不存在', 'error');
                }
            })
            .catch(error => {
                log('❌ gameState.js 載入失敗: ' + error.message, 'error');
            });
        
        // 測試 auth
        import('./js/auth.js')
            .then(module => {
                log('✅ auth.js 載入成功');
            })
            .catch(error => {
                log('❌ auth.js 載入失敗: ' + error.message, 'error');
            });
        
        // 測試 mapService
        import('./js/mapService.js')
            .then(module => {
                log('✅ mapService.js 載入成功');
            })
            .catch(error => {
                log('❌ mapService.js 載入失敗: ' + error.message, 'error');
            });
        
        // 測試 gameValidationService
        import('./js/gameValidationService.js')
            .then(module => {
                log('✅ gameValidationService.js 載入成功');
            })
            .catch(error => {
                log('❌ gameValidationService.js 載入失敗: ' + error.message, 'error');
            });
        
        // 測試 backgroundAnimation
        import('./js/backgroundAnimation.js')
            .then(module => {
                log('✅ backgroundAnimation.js 載入成功');
            })
            .catch(error => {
                log('❌ backgroundAnimation.js 載入失敗: ' + error.message, 'error');
            });
        
        // 測試 game.js (最後測試，因為它依賴其他模組)
        setTimeout(() => {
            import('./js/game.js')
                .then(module => {
                    log('✅ game.js 載入成功');
                    if (module.initGame) {
                        log('✅ initGame 函數存在');
                    } else {
                        log('❌ initGame 函數不存在', 'error');
                    }
                })
                .catch(error => {
                    log('❌ game.js 載入失敗: ' + error.message, 'error');
                });
        }, 2000);
        
        log('測試啟動完成，等待結果...');
    </script>
</body>
</html>
